import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:quran_app/Core/db/database_helper.dart';
import 'package:quran_app/Core/routing/app_router.dart';
import 'package:quran_app/Core/styles/app_colors.dart';
import 'package:quran_app/Features/Home/Presentation/Manager/cubit/theme_cubit.dart';
import 'package:quran_app/Features/Home/Presentation/Views/Widgets/change_theme_widget.dart';

import 'Widgets/surah_item.dart';

class SurahView extends StatefulWidget {
  const SurahView({super.key});

  @override
  State<SurahView> createState() => _SurahViewState();
}

class _SurahViewState extends State<SurahView> {
  bool isSearching = false;
  final TextEditingController? searchController = TextEditingController();

  // function to start searching
  void startSearching() {
    ModalRoute.of(context)!
        .addLocalHistoryEntry(LocalHistoryEntry(onRemove: stopSearching));
    setState(() => isSearching = true);
  }

  // function to stop searching (BackButton)
  void stopSearching() {
    setState(
      () {
        searchSurah = surah
            .where((element) => element['Title']!.toLowerCase().contains(''))
            .toList();
      },
    );
    searchController!.clear();
    setState(() => isSearching = false);
  }

  // function to return app bar actions
  Widget appBarActions() {
    if (isSearching) {
      return [
        IconButton(
          onPressed: () {
            if (searchController!.text.isNotEmpty) {
              searchController!.clear();
              setState(
                () {
                  searchSurah = surah
                      .where((element) =>
                          element['Title']!.toLowerCase().contains(''))
                      .toList();
                },
              );
            } else {
              Navigator.pop(context);
            }
          },
          icon: const Icon(Icons.close),
        )
      ][0];
    } else {
      return [
        IconButton(
          onPressed: startSearching,
          icon: const Icon(Icons.search),
        )
      ][0];
    }
  }

  // function for app bar title when isSearching = false
  Text appBarTitle() {
    return const Text('القرآن الكريم');
  }

  // function for app bar title when isSearching = true
  TextField appBarSearchField() {
    return TextField(
      controller: searchController,
      decoration: const InputDecoration(
        hintText: 'بحث...',
        border: InputBorder.none,
      ),
      cursorColor: AppColors.black,
      onChanged: (String searchingText) {
        setState(() {
          searchSurah = surah
              .where((element) => element['Title']!
                  .toLowerCase()
                  .contains(searchingText.toLowerCase()))
              .toList();
        });
      },
    );
  }

  List<Map<String, dynamic>> surah = [];
  List<Map<String, dynamic>> searchSurah = [];

  @override
  void initState() {
    super.initState();
    getSurah().then((value) {
      setState(() {
        surah = value;
        searchSurah = surah;
      });
    });
  }

  getSurah() async {
    return await DatabaseHelper.instance.querySurahRows();
  }

  String replaceArabicNumber(String input) {
    const english = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    final arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];

    for (int i = 0; i < english.length; i++) {
      input = input.replaceAll(english[i], arabic[i]);
    }

    return input;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: isSearching ? appBarSearchField() : appBarTitle(),
        actions: [
          appBarActions(),
          ChangeThemeWidget(
              themeCubit: BlocProvider.of<ThemeCubit>(context, listen: true))
        ],
      ),
      body: ListView.separated(
        padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 4),
        itemCount: searchSurah.length,
        separatorBuilder: (_, __) => const SizedBox(height: 6),
        itemBuilder: (BuildContext context, int index) {
          return SurahItem(
            title:
                '${replaceArabicNumber((index + 1).toString())} - ${searchSurah[index]['Title']}',
            subTitle: searchSurah[index]['Type'] == 1 ? 'مكية' : 'مدنية',
            trailing:
                'عدد الآيات : ${replaceArabicNumber(searchSurah[index]['Ayat'].toString())}',
            onTap: () {
              GoRouter.of(context).push(
                AppRouter.kQuranView,
                extra: {
                  'id': index + 1,
                  'appBarTitle': searchSurah[index]['Title'],
                },
              );
            },
          );
        },
      ),
    );
  }
}
