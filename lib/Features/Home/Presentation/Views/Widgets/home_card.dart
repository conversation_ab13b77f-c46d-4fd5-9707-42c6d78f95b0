import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:quran_app/Core/styles/app_colors.dart';
import 'package:quran_app/Core/styles/text_styles.dart';
import 'package:quran_app/Core/utils/assets.dart';
import 'package:quran_app/Features/Home/Presentation/Manager/cubit/theme_cubit.dart';

class HomeCard extends StatelessWidget {
  const HomeCard({
    super.key,
    this.title,
    required this.subtitle,
    this.leading,
    this.counter,
    this.realCounter,
    this.onTap,
    this.resetCounter,
  });

  final String? title;
  final String subtitle;
  final String? leading;
  final int? counter;
  final int? realCounter;
  final VoidCallback? onTap;
  final VoidCallback? resetCounter;

  @override
  Widget build(BuildContext context) {
    ThemeCubit themeCubit = BlocProvider.of<ThemeCubit>(context, listen: true);
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4),
      ),
      color: themeCubit.isDark ? AppColors.black : AppColors.white,
      elevation: 0,
      margin: EdgeInsets.zero,
      child: ListTile(
        horizontalTitleGap: 0,
        contentPadding: const EdgeInsets.symmetric(vertical: 2, horizontal: 4),
        visualDensity: const VisualDensity(
          horizontal: -4,
          vertical: -4,
        ),
        minVerticalPadding: 0,
        onTap: onTap != null
            ? counter! > 0
                ? onTap
                : null
            : null,
        title: title != null
            ? Center(
                child: Text(
                  title!,
                  style: AppTextStyles.font22ExtraBold.copyWith(
                    color: AppColors.yellow,
                  ),
                  textAlign: TextAlign.center,
                ),
              )
            : null,
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Text(
                subtitle,
                style: AppTextStyles.font18Medium.copyWith(
                  color: themeCubit.isDark ? AppColors.white : AppColors.black,
                ),
              ),
            ),
            if (leading != null)
              Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: Text(
                  leading!,
                  style: AppTextStyles.font16Light.copyWith(
                    color: themeCubit.isDark
                        ? AppColors.white.withOpacity(0.8)
                        : Colors.grey[900]!,
                  ),
                ),
              ),
            if (counter != null)
              Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: IntrinsicHeight(
                  child: Row(
                    children: [
                      Container(
                        color: AppColors.black,
                        height: 35,
                        child: AspectRatio(
                          aspectRatio: 1,
                          child: Image.asset(AssetsData.logo),
                        ),
                      ),
                      const Spacer(),
                      IntrinsicHeight(
                        child: Row(
                          children: [
                            Container(
                              height: double.infinity,
                              padding: const EdgeInsets.symmetric(
                                  vertical: 4, horizontal: 8),
                              // color: AppColors.black,
                              color: themeCubit.isDark
                                  ? AppColors.white
                                  : AppColors.black,
                              child: Text(
                                counter.toString(),
                                style: AppTextStyles.font20SemiBold.copyWith(
                                  color: realCounter != counter
                                      ? !themeCubit.isDark
                                          ? AppColors.white
                                          : AppColors.black
                                      : AppColors.yellow,
                                ),
                              ),
                            ),
                            GestureDetector(
                              onTap: resetCounter,
                              child: Container(
                                height: double.infinity,
                                padding: const EdgeInsets.all(4),
                                color: AppColors.yellow,
                                child: Icon(
                                  Icons.replay,
                                  // color: AppColors.white,
                                  color: !themeCubit.isDark
                                      ? AppColors.white
                                      : AppColors.black,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
