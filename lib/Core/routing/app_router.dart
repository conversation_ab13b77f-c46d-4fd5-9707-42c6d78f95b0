import 'package:go_router/go_router.dart';
import 'package:quran_app/Features/Home/Presentation/Views/azkar_view.dart';
import 'package:quran_app/Features/Home/Presentation/Views/custom_view.dart';
import 'package:quran_app/Features/Home/Presentation/Views/electronic_rosary_view.dart';
import 'package:quran_app/Features/Home/Presentation/Views/home_view.dart';
import 'package:quran_app/Features/Home/Presentation/Views/prayers_view.dart';
import 'package:quran_app/Features/Home/Presentation/Views/quran_view.dart';
import 'package:quran_app/Features/Home/Presentation/Views/virtues_of_worship.dart';
import 'package:quran_app/Features/Home/Presentation/Views/zakat_calculator_view.dart';

import '../../Features/Home/Presentation/Views/prophetic_hadith_view.dart';
import '../../Features/Home/Presentation/Views/surah_view.dart';

abstract class AppRouter {
  static const kElectronicRosaryView = '/electronic-rosary';
  static const kZakatCalculatorView = '/zakat-calculator';
  static const kVirtuesOfWorshipView = '/virtues-of-worship';
  static const kPrayersView = '/prayers';
  static const kAzkarView = '/azkar-view';
  static const kPropheticHadithView = '/prophetic-hadith-view';
  static const kSurahView = '/surah-view';
  static const kQuranView = '/quran-view';

  // CustomView
  static const kCustomView = '/custom-view';

  static final router = GoRouter(
    routes: [
      GoRoute(
        path: '/',
        builder: (context, state) {
          return const HomeView();
        },
      ),
      GoRoute(
        path: kElectronicRosaryView,
        builder: (context, state) {
          return const ElectronicRosaryView();
        },
      ),
      GoRoute(
        path: kZakatCalculatorView,
        builder: (context, state) {
          return const ZakatCalculator();
        },
      ),
      GoRoute(
        path: kVirtuesOfWorshipView,
        builder: (context, state) {
          return const VirtuesOfWorshipView();
        },
      ),
      GoRoute(
        path: kPrayersView,
        builder: (context, state) {
          return const PrayersView();
        },
      ),
      GoRoute(
        path: kAzkarView,
        builder: (context, state) {
          return const AzkarView();
        },
      ),
      GoRoute(
        path: kSurahView,
        builder: (context, state) {
          return const SurahView();
        },
      ),
      GoRoute(
        path: kQuranView,
        builder: (context, state) {
          return QuranView(
            surahId: (state.extra as Map<String, dynamic>)['id'] as int,
            surahName:
                (state.extra as Map<String, dynamic>)['appBarTitle'] as String,
          );
        },
      ),
      GoRoute(
        path: kPropheticHadithView,
        builder: (context, state) {
          return const PropheticHadithView();
        },
      ),
      GoRoute(
        path: kCustomView,
        builder: (context, state) {
          return CustomView(
            id: (state.extra as Map<String, dynamic>)['id'] as int,
            appBarTitle:
                (state.extra as Map<String, dynamic>)['appBarTitle'] as String,
            isSleeping: (state.extra as Map<String, dynamic>)['isSleeping'] ==
                    null
                ? true
                : (state.extra as Map<String, dynamic>)['isSleeping'] as bool,
          );
        },
      ),
    ],
  );
}
