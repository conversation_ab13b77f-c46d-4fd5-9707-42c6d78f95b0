import 'package:flutter/material.dart';
import 'package:quran_app/Core/styles/app_colors.dart';
import 'package:quran_app/Features/Home/Presentation/Manager/cubit/theme_cubit.dart';

class ChangeThemeWidget extends StatelessWidget {
  const ChangeThemeWidget({
    super.key,
    required this.themeCubit,
  });

  final ThemeCubit themeCubit;

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: () => themeCubit.changeTheme(),
      icon: !themeCubit.isDark
          ? const Icon(
              Icons.light_mode,
              size: 28,
              color: AppColors.white,
            )
          : const Icon(
              Icons.dark_mode,
              size: 28,
              color: AppColors.black,
            ),
    );
  }
}
