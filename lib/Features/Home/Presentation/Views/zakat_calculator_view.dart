import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:quran_app/Core/styles/app_colors.dart';
import 'package:quran_app/Core/styles/text_styles.dart';
import 'package:quran_app/Core/utils/assets.dart';
import 'package:quran_app/Features/Home/Presentation/Manager/cubit/theme_cubit.dart';
import 'package:quran_app/Features/Home/Presentation/Views/Widgets/change_theme_widget.dart';

class ZakatCalculator extends StatefulWidget {
  const ZakatCalculator({super.key});

  @override
  State<ZakatCalculator> createState() => _ZakatCalculatorState();
}

class _ZakatCalculatorState extends State<ZakatCalculator> {
  double? _zakatAmount;

  final TextEditingController _amountController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    ThemeCubit themeCubit = BlocProvider.of<ThemeCubit>(context, listen: true);
    return Scaffold(
      backgroundColor: themeCubit.isDark ? AppColors.black : AppColors.white,
      appBar: AppBar(
        title: const Text('حاسبة الزكاة'),
        actions: [
          ChangeThemeWidget(
              themeCubit: BlocProvider.of<ThemeCubit>(context, listen: true))
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          image: DecorationImage(
              image: const AssetImage(AssetsData.zakat),
              fit: BoxFit.cover,
              opacity: themeCubit.isDark ? 0.2 : 0.3),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(4),
              child: Text(
                'الزكاة هي أحد الأركان الإسلامية الخمسة التي حتّنا الإسلام على أدائها، وللزكاة أجر عظيم عند الله عز وجل؛ لما لها من أهمية كبيرة في بناء المجتمع الإسلامي، وتوثيق العلاقات، وبناء الروابط بين الناس، وسد حاجة الفقراء والمحتاجين وإعانتهم على الحياة.',
                style: AppTextStyles.font16SemiBold.copyWith(
                  color: themeCubit.isDark ? AppColors.white : AppColors.black,
                ),
              ),
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Expanded(
                  child: TextField(
                    onSubmitted: (_) {
                      final int amount =
                          int.tryParse(_amountController.text) ?? 0;
                      if (amount == 0) return;
                      setState(() => _zakatAmount = amount * 2.5 / 100);
                    },
                    controller: _amountController,
                    cursorColor: AppColors.yellow,
                    style: AppTextStyles.font16Medium.copyWith(
                      color:
                          themeCubit.isDark ? AppColors.white : AppColors.black,
                    ),
                    decoration: InputDecoration(
                      hintStyle: AppTextStyles.font16Medium.copyWith(
                        color: themeCubit.isDark
                            ? AppColors.white
                            : AppColors.black,
                      ),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 4),
                      hintText: 'أدخل المبلغ',
                      border: UnderlineInputBorder(
                        borderSide: BorderSide(
                          color: themeCubit.isDark
                              ? AppColors.white
                              : AppColors.black,
                          width: 1,
                        ),
                      ),
                      enabledBorder: UnderlineInputBorder(
                        borderSide: BorderSide(
                          color: themeCubit.isDark
                              ? AppColors.white
                              : AppColors.black,
                          width: 1,
                        ),
                      ),
                      focusedBorder: UnderlineInputBorder(
                        borderSide: BorderSide(
                          color: themeCubit.isDark
                              ? AppColors.white
                              : AppColors.black,
                          width: 2,
                        ),
                      ),
                    ),
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    keyboardType: TextInputType.number,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    final int amount =
                        int.tryParse(_amountController.text) ?? 0;
                    if (amount == 0) return;
                    setState(() => _zakatAmount = amount * 2.5 / 100);
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 6,
                    ),
                    color:
                        themeCubit.isDark ? AppColors.white : AppColors.black,
                    child: Text(
                      'حساب الزكاة',
                      style: AppTextStyles.font16SemiBold.copyWith(
                        // color: AppColors.white,
                        color: !themeCubit.isDark
                            ? AppColors.white
                            : AppColors.black,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (_zakatAmount != null)
              Padding(
                padding: const EdgeInsets.all(4),
                child: Text(
                  'مقدار الزكاة : ${_zakatAmount?.toStringAsFixed(0)}',
                  style: AppTextStyles.font16SemiBold.copyWith(
                    color:
                        themeCubit.isDark ? AppColors.white : AppColors.black,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
