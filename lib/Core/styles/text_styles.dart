import 'package:flutter/material.dart';

abstract class AppTextStyles {
  // bold 24
  static const TextStyle font24Bold = TextStyle(
    fontSize: 24,
    fontFamily: 'Font',
    fontWeight: FontWeight.w700,
  );

  // semi bold 24
  static const TextStyle font24SemiBold = TextStyle(
    fontSize: 24,
    fontFamily: 'Font',
    fontWeight: FontWeight.w600,
  );

  // bold 16
  static const TextStyle font16Bold = TextStyle(
    fontSize: 16,
    fontFamily: 'Font',
    fontWeight: FontWeight.w700,
  );

  // 16 600
  static const TextStyle font16SemiBold = TextStyle(
    fontSize: 16,
    fontFamily: 'Font',
    fontWeight: FontWeight.w600,
  );

  // 18 600
  static const TextStyle font18SemiBold = TextStyle(
    fontSize: 18,
    fontFamily: 'Font',
    fontWeight: FontWeight.w600,
  );

  // bold 14
  static const TextStyle font14Bold = TextStyle(
    fontSize: 14,
    fontFamily: 'Font',
    fontWeight: FontWeight.w700,
  );

  // semi bold 10
  static const TextStyle font10SemiBold = TextStyle(
    fontSize: 10,
    fontFamily: 'Font',
    fontWeight: FontWeight.w600,
  );

  // medium 16
  static const TextStyle font16Medium = TextStyle(
    fontSize: 16,
    fontFamily: 'Font',
    fontWeight: FontWeight.w500,
  );

  // medium 14
  static const TextStyle font14Medium = TextStyle(
    fontSize: 14,
    fontFamily: 'Font',
    fontWeight: FontWeight.w500,
  );

  // regular 14
  static const TextStyle font14Regular = TextStyle(
    fontSize: 14,
    fontFamily: 'Font',
    fontWeight: FontWeight.w400,
  );

  // 14 300
  static const TextStyle font14Light = TextStyle(
    fontSize: 14,
    fontFamily: 'Font',
    fontWeight: FontWeight.w300,
  );

  static const TextStyle font11Light = TextStyle(
    fontSize: 11,
    fontFamily: 'Font',
    fontWeight: FontWeight.w300,
  );

  // 13 400
  static const TextStyle font13Regular = TextStyle(
    fontSize: 13,
    fontFamily: 'Font',
    fontWeight: FontWeight.w400,
  );

  // 13 500
  static const TextStyle font13Medium = TextStyle(
    fontSize: 13,
    fontFamily: 'Font',
    fontWeight: FontWeight.w500,
  );

  // 11 500
  static const TextStyle font11Medium = TextStyle(
    fontSize: 11,
    fontFamily: 'Font',
    fontWeight: FontWeight.w500,
  );

  static const TextStyle font10Light = TextStyle(
    fontSize: 10,
    fontFamily: 'Font',
    fontWeight: FontWeight.w300,
  );

  // medium 12

  static const TextStyle font12Medium = TextStyle(
    fontSize: 12,
    fontFamily: 'Font',
    fontWeight: FontWeight.w500,
  );

  // regular 16
  static const TextStyle font16Regular = TextStyle(
    fontSize: 16,
    fontFamily: 'Font',
    fontWeight: FontWeight.w400,
  );

  static const TextStyle font16Light = TextStyle(
    fontSize: 15,
    fontFamily: 'Font',
    fontWeight: FontWeight.w100,
  );

  // regular 12
  static const TextStyle font12Regular = TextStyle(
    fontSize: 12,
    fontFamily: 'Font',
    fontWeight: FontWeight.w400,
  );

  // regular 10
  static const TextStyle font10Regular = TextStyle(
    fontSize: 10,
    fontWeight: FontWeight.w400,
  );

  // regular 8
  static const TextStyle font8Regular = TextStyle(
    fontSize: 8,
    fontFamily: 'Font',
    fontWeight: FontWeight.w400,
  );

  // 32 600
  static const TextStyle font32SemiBold = TextStyle(
    fontSize: 32,
    fontFamily: 'Font',
    fontWeight: FontWeight.w600,
  );

  // 18 500
  static const TextStyle font18Medium = TextStyle(
    fontSize: 18,
    fontFamily: 'Font',
    fontWeight: FontWeight.w500,
  );

  // 14 600
  static const TextStyle font14SemiBold = TextStyle(
    fontSize: 14,
    fontFamily: 'Font',
    fontWeight: FontWeight.w600,
  );
  //20 600
  static const TextStyle font20SemiBold = TextStyle(
    fontSize: 20,
    fontFamily: 'Font',
    fontWeight: FontWeight.w600,
  );

  // 20 700
  static const TextStyle font20Bold = TextStyle(
    fontSize: 20,
    fontFamily: 'Font',
    fontWeight: FontWeight.w700,
  );

  // 20 700
  static const TextStyle font20ExtraBold = TextStyle(
    fontSize: 20,
    fontFamily: 'Font',
    fontWeight: FontWeight.w800,
  );

  static const TextStyle font20Black = TextStyle(
    fontSize: 20,
    fontFamily: 'Font',
    fontWeight: FontWeight.w900,
  );

  static const TextStyle font22ExtraBold = TextStyle(
    fontSize: 22,
    fontFamily: 'Font',
    fontWeight: FontWeight.w800,
  );

  static const TextStyle font22Black = TextStyle(
    fontSize: 22,
    fontFamily: 'Font',
    fontWeight: FontWeight.w900,
  );
  static const TextStyle font24ExtraBold = TextStyle(
    fontSize: 24,
    fontFamily: 'Font',
    fontWeight: FontWeight.w800,
  );

  static const TextStyle font24Black = TextStyle(
    fontSize: 24,
    fontFamily: 'Font',
    fontWeight: FontWeight.w900,
  );

  // 20 500
  static const TextStyle font20Medium = TextStyle(
    fontSize: 20,
    fontFamily: 'Font',
    fontWeight: FontWeight.w500,
  );

  // 12 600
  static const TextStyle font12SemiBold = TextStyle(
    fontSize: 12,
    fontFamily: 'Font',
    fontWeight: FontWeight.w600,
  );

  //24 500
  static const TextStyle font24Medium = TextStyle(
    fontSize: 24,
    fontFamily: 'Font',
    fontWeight: FontWeight.w500,
  );

  static const TextStyle font24MediumMeQuran = TextStyle(
    fontSize: 26,
    fontFamily: 'MeQuran',
    fontWeight: FontWeight.w100,
    height: 2,
  );

  // 28 600
  static const TextStyle font28SemiBold = TextStyle(
    fontSize: 28,
    fontFamily: 'Font',
    fontWeight: FontWeight.w600,
  );

  // 10 500
  static const TextStyle font10Medium = TextStyle(
    fontSize: 10,
    fontFamily: 'Font',
    fontWeight: FontWeight.w500,
  );
}
