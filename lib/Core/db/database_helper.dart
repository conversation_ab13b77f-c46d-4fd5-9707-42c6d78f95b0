import 'dart:io';

import 'package:flutter/services.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';

class DatabaseHelper {
  // This is the actual database filename that is saved in the docs directory.
  static const _databaseName = "database.sqlite";
  static const _databaseVersion = 1;

  static const _categoryTableName = 'Category';
  static const _itemsTableName = 'Items';
  static const _surahTableName = 'Surah';
  static const _quranTableName = 'Quran';

  late Database _db;

  // This is a singleton class. This provides a way to create only one object
  // of this class. You can call this class by DatabaseHelper.instance and this
  // will return you the same object. So, there will be only one object of this
  // class in your entire application
  DatabaseHelper._privateConstructor();
  static final DatabaseHelper instance = DatabaseHelper._privateConstructor();

  // this opens the database (and creates it if it doesn't exist)
  Future<void> init() async {
    // Get a location using getDatabasesPath
    var databasesPath = await getDatabasesPath();
    String path = join(databasesPath, _databaseName);

    // Check if the database file exists in the device's local storage directory
    bool exists = await databaseExists(path);

    // If the database file does not exist, copy it from assets
    if (!exists) {
      // Make sure the parent directory exists
      await Directory(dirname(path)).create(recursive: true);

      // Copy the database file from assets to the device's local storage directory
      ByteData data = await rootBundle.load(join("assets", _databaseName));
      List<int> bytes =
          data.buffer.asUint8List(data.offsetInBytes, data.lengthInBytes);
      await File(path).writeAsBytes(bytes);
    }

    // Open the database
    _db = await openDatabase(
      path,
      version: _databaseVersion,
    );
  }

  // All of the rows are returned as a list of maps, where each map is
  // a key-value list of columns.
  Future<List<Map<String, dynamic>>> queryAllRowsCategories() async {
    var x = await _db.query(_categoryTableName);
    return x;
  }

  Future<List<Map<String, dynamic>>> queryAllRowsItems() async {
    var x = await _db.query(_itemsTableName);
    return x;
  }

  Future<List<Map<String, dynamic>>> queryCategoriesRowsByTypeAndPid(
      int type, int pid) async {
    return await _db.query(_categoryTableName,
        where: 'type = ? AND pid = ?', whereArgs: [type, pid]);
  }

  Future<List<Map<String, dynamic>>> queryItemsRowsByCategoryId(
      int categoryId) async {
    return await _db
        .query(_itemsTableName, where: 'category = ?', whereArgs: [categoryId]);
  }

  Future<List<Map<String, dynamic>>> querySurahRows() async {
    return await _db.query(_surahTableName);
  }

  Future<List<Map<String, dynamic>>> queryQuranRowsByChapterId(
      int chapterId) async {
    return await _db
        .query(_quranTableName, where: 'ChapterID = ?', whereArgs: [chapterId]);
  }
}
