import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:quran_app/Core/styles/app_colors.dart';
import 'package:quran_app/Core/styles/text_styles.dart';
import 'package:quran_app/Core/utils/assets.dart';
import 'package:quran_app/Core/utils/constants.dart';
import 'package:quran_app/Features/Home/Presentation/Manager/cubit/theme_cubit.dart';
import 'package:quran_app/Features/Home/Presentation/Views/Widgets/change_theme_widget.dart';

class ElectronicRosaryView extends StatefulWidget {
  const ElectronicRosaryView({super.key});

  @override
  State<ElectronicRosaryView> createState() => _ElectronicRosaryViewState();
}

class _ElectronicRosaryViewState extends State<ElectronicRosaryView> {
  int _counter = Hive.box<int>(AppConstants.rosaryBoxName)
      .get(AppConstants.rosaryKey, defaultValue: 0)!;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: BlocProvider.of<ThemeCubit>(context).isDark
          ? AppColors.black
          : AppColors.grey,
      appBar: AppBar(
        title: const Text('السبحة الإلكترونية'),
        actions: [
          IconButton(
            icon: Icon(
              Icons.replay_rounded,
              color: BlocProvider.of<ThemeCubit>(context).isDark
                  ? AppColors.black
                  : AppColors.white,
              size: 30,
            ),
            onPressed: () {
              if (_counter > 0) {
                setState(() => _counter = 0);
                Hive.box<int>(AppConstants.rosaryBoxName)
                    .put(AppConstants.rosaryKey, _counter);
              }
            },
          ),
          ChangeThemeWidget(
              themeCubit: BlocProvider.of<ThemeCubit>(context, listen: true)),
        ],
      ),
      body: GestureDetector(
        onTap: () {
          setState(() => _counter++);
          Hive.box<int>(AppConstants.rosaryBoxName)
              .put(AppConstants.rosaryKey, _counter);
        },
        child: Container(
          decoration:  BoxDecoration(
            image: DecorationImage(
              image: AssetImage(AssetsData.sebha),
              fit: BoxFit.cover,
              opacity: BlocProvider.of<ThemeCubit>(context).isDark
                  ? 0.2 : 0.3
            ),
          ),
          child: Center(
            child: Text(
              '$_counter',
              style: AppTextStyles.font32SemiBold.copyWith(
                color: BlocProvider.of<ThemeCubit>(context).isDark
                    ? AppColors.white
                    : AppColors.black,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
