import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:quran_app/Core/styles/app_colors.dart';
import 'package:quran_app/Core/styles/text_styles.dart';
import 'package:quran_app/Features/Home/Presentation/Manager/cubit/theme_cubit.dart';

class SurahItem extends StatelessWidget {
  const SurahItem(
      {super.key,
      required this.title,
      required this.subTitle,
      required this.trailing,
      this.onTap});

  final String title;
  final String subTitle;
  final String trailing;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4),
      ),
      color: BlocProvider.of<ThemeCubit>(context).isDark
          ? AppColors.black
          : AppColors.white,
      elevation: 0,
      margin: EdgeInsets.zero,
      child: ListTile(
        contentPadding:
            const EdgeInsets.only(top: 12, bottom: 6, left: 6, right: 6),
        horizontalTitleGap: 0,
        visualDensity: const VisualDensity(
          horizontal: -4,
          vertical: -4,
        ),
        minVerticalPadding: 0,
        minLeadingWidth: 0,
        onTap: onTap,
        title: Text(
          title,
          style: AppTextStyles.font18Medium.copyWith(
            color: AppColors.yellow,
          ),
        ),
        subtitle: Text(
          subTitle,
          style: AppTextStyles.font14Medium,
        ),
        trailing: Text(
          trailing,
          style: AppTextStyles.font14Medium,
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
