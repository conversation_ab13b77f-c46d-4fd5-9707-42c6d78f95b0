import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:quran_app/Core/routing/app_router.dart';
import 'package:quran_app/Core/styles/app_colors.dart';
import 'package:quran_app/Core/styles/text_styles.dart';
import 'package:quran_app/Core/db/database_helper.dart';
import 'package:quran_app/Core/utils/constants.dart';
import 'package:quran_app/Features/Home/Presentation/Manager/cubit/theme_cubit.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Hive.initFlutter();
  await Hive.openBox<int>(AppConstants.rosaryBoxName);
  await Hive.openBox<bool>(AppConstants.themeBoxName);

  await DatabaseHelper.instance.init();
  runApp(
    BlocProvider(
      create: (_) => ThemeCubit(),
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    ThemeCubit themeCubit = BlocProvider.of<ThemeCubit>(context, listen: true);
    return MaterialApp.router(
      localizationsDelegates: const [
        GlobalCupertinoLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ],
      supportedLocales: const [Locale('ar', 'EG')],
      locale: const Locale('ar', 'EG'),
      routerConfig: AppRouter.router,
      debugShowCheckedModeBanner: false,
      theme: themeCubit.isDark
          ? ThemeData.dark().copyWith(
              scaffoldBackgroundColor: AppColors.grey,
              appBarTheme: AppBarTheme(
                centerTitle: true,
                backgroundColor: AppColors.yellow,
                titleTextStyle: AppTextStyles.font20SemiBold.copyWith(
                  color: AppColors.black,
                ),
                iconTheme: const IconThemeData(color: AppColors.black),
              ),
            )
          : ThemeData.light().copyWith(
              scaffoldBackgroundColor: AppColors.grey,
              appBarTheme: const AppBarTheme(
                centerTitle: true,
                backgroundColor: AppColors.yellow,
                titleTextStyle: AppTextStyles.font20SemiBold,
                iconTheme: IconThemeData(color: AppColors.white),
              ),
            ),
    );
  }
}
