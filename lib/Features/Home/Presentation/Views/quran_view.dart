import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:quran_app/Core/db/database_helper.dart';
import 'package:quran_app/Core/styles/app_colors.dart';
import 'package:quran_app/Core/styles/text_styles.dart';
import 'package:quran_app/Features/Home/Presentation/Manager/cubit/theme_cubit.dart';
import 'package:quran_app/Features/Home/Presentation/Views/Widgets/change_theme_widget.dart';
import 'package:wakelock/wakelock.dart';

class QuranView extends StatefulWidget {
  const QuranView({super.key, required this.surahId, required this.surahName});
  final int surahId;
  final String surahName;
  @override
  State<QuranView> createState() => _QuranViewState();
}

class _QuranViewState extends State<QuranView> {
  List<Map<String, dynamic>> quran = [];

  @override
  void initState() {
    super.initState();
    getSurah().then(
      (value) {
        setState(() => quran = value);
      },
    );
    Wakelock.enable();
  }

  @override
  void dispose() {
    super.dispose();
    Wakelock.disable();
  }

  getSurah() async {
    return await DatabaseHelper.instance
        .queryQuranRowsByChapterId(widget.surahId);
  }

  String replaceArabicNumber(String input) {
    const english = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
    final arabic = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];

    for (int i = 0; i < english.length; i++) {
      input = input.replaceAll(english[i], arabic[i]);
    }

    return input;
  }

  @override
  Widget build(BuildContext context) {
    ThemeCubit themeCubit = BlocProvider.of<ThemeCubit>(context, listen: true);
    return Scaffold(
      backgroundColor: themeCubit.isDark ? AppColors.black : AppColors.white,
      appBar: AppBar(
        title: Text(widget.surahName),
        actions: [
          ChangeThemeWidget(
              themeCubit: BlocProvider.of<ThemeCubit>(context, listen: true))
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: SingleChildScrollView(
          child: Column(
            children: [
              if (widget.surahId != 1)
                Text(
                  'بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ',
                  style: AppTextStyles.font24MediumMeQuran.copyWith(
                    color:
                        themeCubit.isDark ? AppColors.white : AppColors.black,
                  ),
                ),
              RichText(
                text: TextSpan(
                  style: AppTextStyles.font24MediumMeQuran.copyWith(
                    color:
                        themeCubit.isDark ? AppColors.white : AppColors.black,
                  ),
                  children: [
                    for (var item in quran)
                      TextSpan(
                        text: item['AyahText'],
                        children: [
                          TextSpan(
                            text:
                                '  {${replaceArabicNumber(item['VerseIdBegin'].toString())}}  ',
                            style: AppTextStyles.font24MediumMeQuran.copyWith(
                              color: AppColors.yellow,
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
            ],
          ),
        ),
      ),
    );
  }
}
