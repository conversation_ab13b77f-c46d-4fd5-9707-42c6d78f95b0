import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:meta/meta.dart';
import 'package:quran_app/Core/utils/constants.dart';

part 'theme_state.dart';

class ThemeCubit extends Cubit<ThemeState> {
  ThemeCubit() : super(ThemeInitial());

  bool _isDark = Hive.box<bool>(AppConstants.themeBoxName)
          .get(AppConstants.themeKey, defaultValue: false) ??
      false;
  bool get isDark => _isDark;

  void changeTheme() {
    emit(ThemeChangedLoading());
    Hive.box<bool>(AppConstants.themeBoxName)
        .put(AppConstants.themeKey, !_isDark);
    _isDark = !_isDark;
    emit(ThemeChangedSuccess());
  }
}
