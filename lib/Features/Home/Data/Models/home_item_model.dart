import 'package:go_router/go_router.dart';
import 'package:quran_app/Core/routing/app_router.dart';
import 'package:quran_app/Core/utils/assets.dart';

class HomeItemModel {
  final String title;
  final String? image;
  final String? route;
  final void Function()? onTap;

  HomeItemModel({required this.title, this.image, this.route, this.onTap});

  static List<HomeItemModel> items(context) => [
        HomeItemModel(
          title: 'القرآن الكريم',
          image: AssetsData.icon1,
          onTap: () {
            GoRouter.of(context).push(AppRouter.kSurahView);
          },
        ),
        HomeItemModel(
          title: 'أحاديث نبوية',
          image: AssetsData.icon2,
          onTap: () {
            GoRouter.of(context).push(AppRouter.kPropheticHadithView);
          },
        ),
        HomeItemModel(
          title: 'دعاء ختم القرآن الكريم',
          image: AssetsData.icon3,
          onTap: () {
            GoRouter.of(context).push(
              AppRouter.kCustomView,
              extra: {
                'id': 20,
                'appBarTitle': 'دعاء ختم القرآن الكريم',
              },
            );
          },
        ),
        HomeItemModel(
          title: 'الرقية الشرعية',
          image: AssetsData.icon4,
          onTap: () {
            GoRouter.of(context).push(
              AppRouter.kCustomView,
              extra: {
                'id': 27,
                'appBarTitle': 'الرقية الشرعية',
              },
            );
          },
        ),
        HomeItemModel(
          title: 'تسابيح',
          image: AssetsData.icon5,
          onTap: () {
            GoRouter.of(context).push(
              AppRouter.kCustomView,
              extra: {
                'id': 3,
                'appBarTitle': 'تسابيح',
              },
            );
          },
        ),
        HomeItemModel(
          title: 'أذكار',
          image: AssetsData.icon6,
          onTap: () {
            GoRouter.of(context).push(AppRouter.kAzkarView);
          },
        ),
        HomeItemModel(
          title: 'أدعية',
          image: AssetsData.icon7,
          onTap: () {
            GoRouter.of(context).push(AppRouter.kPrayersView);
          },
        ),
        HomeItemModel(
          title: 'فضائل العبادات',
          image: AssetsData.icon8,
          onTap: () {
            GoRouter.of(context).push(AppRouter.kVirtuesOfWorshipView);
          },
        ),
        HomeItemModel(
          title: 'أسماء الله الحسنى',
          image: AssetsData.icon9,
          onTap: () {
            GoRouter.of(context).push(
              AppRouter.kCustomView,
              extra: {
                'id': 25,
                'appBarTitle': 'أسماء الله الحسنى',
              },
            );
          },
        ),
        HomeItemModel(
          title: 'السبحة الإلكترونية',
          image: AssetsData.icon10,
          onTap: () {
            GoRouter.of(context).push(AppRouter.kElectronicRosaryView);
          },
        ),
        HomeItemModel(
          title: 'حاسبة الزكاة',
          image: AssetsData.icon11,
          onTap: () {
            GoRouter.of(context).push(AppRouter.kZakatCalculatorView);
          },
        ),
      ];
}
