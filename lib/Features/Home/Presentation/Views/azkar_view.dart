import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:quran_app/Core/db/database_helper.dart';
import 'package:quran_app/Core/routing/app_router.dart';
import 'package:quran_app/Core/utils/assets.dart';
import 'package:quran_app/Features/Home/Presentation/Manager/cubit/theme_cubit.dart';
import 'package:quran_app/Features/Home/Presentation/Views/Widgets/change_theme_widget.dart';
import 'package:quran_app/Features/Home/Presentation/Views/Widgets/home_item.dart';

class AzkarView extends StatefulWidget {
  const AzkarView({super.key});

  @override
  State<AzkarView> createState() => _AzkarViewState();
}

class _AzkarViewState extends State<AzkarView> {
  List<Map<String, dynamic>> azkar = [];

  @override
  void initState() {
    super.initState();
    getAzkar().then((value) {
      setState(() {
        azkar = value;
      });
    });
  }

  getAzkar() async {
    return await DatabaseHelper.instance.queryCategoriesRowsByTypeAndPid(3, 28);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('أذكار'),
        actions: [ChangeThemeWidget(themeCubit: BlocProvider.of<ThemeCubit>(context, listen: true))],
      ),
      body: ListView.separated(
        padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 4),
        itemCount: azkar.length,
        separatorBuilder: (_, __) => const SizedBox(height: 6),
        itemBuilder: (BuildContext context, int index) {
          return HomeItem(
            title: azkar[index]['title'],
            image: AssetsData.logo,
            hasBackground: true,
            onTap: () {
              GoRouter.of(context).push(
                AppRouter.kCustomView,
                extra: {
                  'id': azkar[index]['corder'],
                  'appBarTitle': azkar[index]['title'],
                  'isSleeping' : false,
                },
              );
            },
          );
        },
      ),
    );
  }
}
