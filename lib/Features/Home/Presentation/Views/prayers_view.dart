import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:quran_app/Core/db/database_helper.dart';
import 'package:quran_app/Core/routing/app_router.dart';
import 'package:quran_app/Core/utils/assets.dart';
import 'package:quran_app/Features/Home/Presentation/Manager/cubit/theme_cubit.dart';
import 'package:quran_app/Features/Home/Presentation/Views/Widgets/change_theme_widget.dart';
import 'package:quran_app/Features/Home/Presentation/Views/Widgets/home_item.dart';

class PrayersView extends StatefulWidget {
  const PrayersView({super.key});

  @override
  State<PrayersView> createState() => _PrayersViewState();
}

class _PrayersViewState extends State<PrayersView> {
  List<Map<String, dynamic>> prayers = [];

  @override
  void initState() {
    super.initState();
    getPrayers().then((value) {
      setState(() {
        prayers = value;
      });
    });
  }

  getPrayers() async {
    return await DatabaseHelper.instance.queryCategoriesRowsByTypeAndPid(3, 29);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('أدعية'),
        actions: [ChangeThemeWidget(themeCubit: BlocProvider.of<ThemeCubit>(context, listen: true))],
      ),
      body: ListView.separated(
        padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 4),
        itemCount: prayers.length,
        separatorBuilder: (_, __) => const SizedBox(height: 6),
        itemBuilder: (BuildContext context, int index) {
          return HomeItem(
            title: prayers[index]['title'],
            image: AssetsData.logo,
            hasBackground: true,
            onTap: () {
              GoRouter.of(context).push(
                AppRouter.kCustomView,
                extra: {
                  'id': prayers[index]['corder'],
                  'appBarTitle': prayers[index]['title'],
                  'isSleeping' : false,
                },
              );
            },
          );
        },
      ),
    );
  }
}
