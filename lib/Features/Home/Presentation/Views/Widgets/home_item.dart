import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:quran_app/Core/styles/app_colors.dart';
import 'package:quran_app/Core/styles/text_styles.dart';
import 'package:quran_app/Features/Home/Presentation/Manager/cubit/theme_cubit.dart';
import 'package:flutter_svg/flutter_svg.dart';

class HomeItem extends StatelessWidget {
  const HomeItem({
    super.key,
    required this.title,
    this.image,
    this.onTap,
    this.isSvg = false,
    this.hasBackground = false,
  });

  final String title;
  final String? image;
  final void Function()? onTap;
  final bool isSvg;
  final bool hasBackground;

  @override
  Widget build(BuildContext context) {
    return Card(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4),
      ),
      color: BlocProvider.of<ThemeCubit>(context).isDark
          ? AppColors.black
          : AppColors.white,
      elevation: 0,
      margin: EdgeInsets.zero,
      child: ListTile(
        contentPadding: const EdgeInsets.all(8),
        horizontalTitleGap: 0,
        visualDensity: VisualDensity.compact,
        minVerticalPadding: 0,
        minLeadingWidth: 0,
        onTap: onTap,
        title: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4),
          child: Text(
            title,
            style: AppTextStyles.font20SemiBold.copyWith(
              color: AppColors.yellow,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        leading: image != null
            ? (isSvg
                ? Container(
                    width: 45,
                    height: 45,
                    alignment: Alignment.center,
                    color: Colors.transparent,
                    child: SvgPicture.asset(image!),
                  )
                : hasBackground
                    ? Container(
                        color: AppColors.black,
                        child: Image.asset(image!, width: 45, height: 45),
                      )
                    : Image.asset(image!, width: 45, height: 45))
            : null,
      ),
    );
  }
}
