import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:quran_app/Core/db/database_helper.dart';
import 'package:quran_app/Features/Home/Presentation/Manager/cubit/theme_cubit.dart';
import 'package:quran_app/Features/Home/Presentation/Views/Widgets/change_theme_widget.dart';

import 'package:wakelock/wakelock.dart';
import 'Widgets/home_card.dart';

class CustomView extends StatefulWidget {
  const CustomView({
    super.key,
    required this.appBarTitle,
    required this.id,
    this.isSleeping = true,
  });

  final String appBarTitle;
  final int id;
  final bool isSleeping;

  @override
  State<CustomView> createState() => _CustomViewState();
}

class _CustomViewState extends State<CustomView> {
  List<Map<String, dynamic>> items = [];
  List<int>? counters = [];
  List<int>? realCounters = [];

  @override
  void initState() {
    super.initState();
    getData().then((value) {
      setState(() {
        items = value;
        for (var item in items) {
          if (item['count'] == 0) return;
          counters!.add(item['count']);
          realCounters!.add(item['count']);
        }
      });
    });
    if (!widget.isSleeping) {
      Wakelock.enable();
    }
  }

  @override
  void dispose() {
    super.dispose();
    if (!widget.isSleeping) {
      Wakelock.disable();
    }
  }

  void decrementCounter(int index) {
    setState(() {
      counters![index] = (counters![index] - 1);
    });
  }

  void resetCounter(int index) {
    if (counters![index] == realCounters![index]) return;
    setState(() {
      counters![index] = realCounters![index];
    });
  }

  getData() async {
    return await DatabaseHelper.instance.queryItemsRowsByCategoryId(widget.id);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.appBarTitle),
        actions: [
          ChangeThemeWidget(
              themeCubit: BlocProvider.of<ThemeCubit>(context, listen: true))
        ],
      ),
      body: ListView.separated(
        padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 4),
        itemCount: items.length,
        separatorBuilder: (_, __) => const SizedBox(height: 6),
        itemBuilder: (BuildContext context, int index) {
          return HomeCard(
            key: UniqueKey(),
            title: items[index]['pre'],
            subtitle: items[index]['content'],
            leading: items[index]['desc'],
            counter: (counters == null || counters!.isEmpty)
                ? null
                : counters![index],
            onTap: (counters == null || counters!.isEmpty)
                ? null
                : () => decrementCounter(index),
            resetCounter: () => resetCounter(index),
            realCounter: (realCounters == null || realCounters!.isEmpty)
                ? null
                : realCounters![index],
          );
        },
      ),
    );
  }
}
