import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:quran_app/Core/db/database_helper.dart';
import 'package:quran_app/Core/styles/app_colors.dart';
import 'package:quran_app/Core/styles/text_styles.dart';
import 'package:quran_app/Core/utils/assets.dart';
import 'package:quran_app/Features/Home/Presentation/Manager/cubit/theme_cubit.dart';
import 'package:share_plus/share_plus.dart';

import '../../Data/Models/home_item_model.dart';
import 'Widgets/change_theme_widget.dart';
import 'Widgets/home_item.dart';

class HomeView extends StatefulWidget {
  const HomeView({super.key});

  @override
  State<HomeView> createState() => _HomeViewState();
}

class _HomeViewState extends State<HomeView> {
  @override
  void initState() {
    DatabaseHelper.instance.queryAllRowsCategories();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    ThemeCubit themeCubit = BlocProvider.of<ThemeCubit>(context, listen: true);
    return Scaffold(
      body: Container(
        color: AppColors.grey,
        child: SafeArea(
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: themeCubit.isDark
                      ? AppColors.black.withOpacity(0.8)
                      : AppColors.grey,
                  image:  DecorationImage(
                    image: const AssetImage(AssetsData.head),
                    fit: BoxFit.contain,
                    opacity: themeCubit.isDark ? 0.2 : 0.3
                  ),
                ),
                height: 200,
                child: Center(
                  child: Column(
                    children: [
                      Align(
                        alignment: Alignment.topLeft,
                        child: ChangeThemeWidget(themeCubit: themeCubit),
                      ),
                      const Spacer(),
                      Align(
                        alignment: Alignment.bottomLeft,
                        child: TextButton.icon(
                          style: TextButton.styleFrom(
                            backgroundColor: AppColors.yellow,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(0),
                            ),
                          ),
                          onPressed: () {
                            Share.share(
                              'http://play.google.com/store/apps/details?id=com.musliem.mayetallah',
                            );
                          },
                          label: Icon(
                            Icons.share,
                            size: 24,
                            color: !themeCubit.isDark
                                ? AppColors.black
                                : AppColors.white,
                          ),
                          icon: Text(
                            'شارك التطبيق',
                            style: AppTextStyles.font18Medium.copyWith(
                              color: !themeCubit.isDark
                                  ? AppColors.black
                                  : AppColors.white,
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ),
              Expanded(
                child: ListView.separated(
                  padding: const EdgeInsets.all(2),
                  itemCount: HomeItemModel.items(context).length,
                  separatorBuilder: (_, __) => const SizedBox(height: 6),
                  itemBuilder: (BuildContext context, int index) {
                    return HomeItem(
                      title: HomeItemModel.items(context)[index].title,
                      image: HomeItemModel.items(context)[index].image,
                      onTap: HomeItemModel.items(context)[index].onTap,
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
