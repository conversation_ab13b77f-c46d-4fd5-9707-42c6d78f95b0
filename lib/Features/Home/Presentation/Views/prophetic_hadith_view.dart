import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:quran_app/Core/db/database_helper.dart';
import 'package:quran_app/Core/routing/app_router.dart';
import 'package:quran_app/Core/utils/assets.dart';
import 'package:quran_app/Features/Home/Presentation/Manager/cubit/theme_cubit.dart';
import 'package:quran_app/Features/Home/Presentation/Views/Widgets/change_theme_widget.dart';
import 'package:quran_app/Features/Home/Presentation/Views/Widgets/home_item.dart';

class PropheticHadithView extends StatefulWidget {
  const PropheticHadithView({super.key});

  @override
  State<PropheticHadithView> createState() => _PropheticHadithViewState();
}

class _PropheticHadithViewState extends State<PropheticHadithView> {
  List<Map<String, dynamic>> propheticHadith = [];

  List<int> numbers = List.generate(86 - 34 + 1, (index) => index + 34)
      .where((number) => number != 60 && number != 47)
      .toList();

  @override
  initState() {
    super.initState();
    getPropheticHadith().then((value) {
      setState(() {
        propheticHadith = value;
      });
    });
  }

  getPropheticHadith() async {
    return await DatabaseHelper.instance.queryCategoriesRowsByTypeAndPid(3, 33);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('أحاديث نبوية'),
        actions: [ChangeThemeWidget(themeCubit: BlocProvider.of<ThemeCubit>(context, listen: true))],
      ),
      body: ListView.separated(
        padding: const EdgeInsets.symmetric(horizontal: 2,vertical: 4),
        itemCount: propheticHadith.length,
        separatorBuilder: (_, __) => const SizedBox(height: 6),
        itemBuilder: (BuildContext context, int index) {
          return HomeItem(
            title: propheticHadith[index]['title'],
            image: AssetsData.logo,
            hasBackground: true,
            onTap: () {
              GoRouter.of(context).push(
                AppRouter.kCustomView,
                extra: {
                  'id': numbers[index],
                  'appBarTitle': propheticHadith[index]['title'],
                  'isSleeping' : false,
                },
              );
            },
          );
        },
      ),
    );
  }
}
