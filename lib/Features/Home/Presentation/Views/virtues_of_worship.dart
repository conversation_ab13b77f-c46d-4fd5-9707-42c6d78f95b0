import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:quran_app/Core/db/database_helper.dart';
import 'package:quran_app/Core/routing/app_router.dart';
import 'package:quran_app/Core/utils/assets.dart';
import 'package:quran_app/Features/Home/Presentation/Manager/cubit/theme_cubit.dart';
import 'package:quran_app/Features/Home/Presentation/Views/Widgets/change_theme_widget.dart';
import 'package:quran_app/Features/Home/Presentation/Views/Widgets/home_item.dart';

class VirtuesOfWorshipView extends StatefulWidget {
  const VirtuesOfWorshipView({super.key});

  @override
  State<VirtuesOfWorshipView> createState() => _VirtuesOfWorshipViewState();
}

class _VirtuesOfWorshipViewState extends State<VirtuesOfWorshipView> {
  List<Map<String, dynamic>> virtuesOfWorship = [];

  @override
  initState() {
    super.initState();
    getVirtuesOfWorship().then((value) {
      setState(() {
        virtuesOfWorship = value;
      });
    });
  }

  getVirtuesOfWorship() async {
    return await DatabaseHelper.instance.queryCategoriesRowsByTypeAndPid(3, 30);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('فضائل العبادات'),
        actions: [ChangeThemeWidget(themeCubit: BlocProvider.of<ThemeCubit>(context, listen: true))],
      ),
      body: ListView.separated(
        padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 4),
        itemCount: virtuesOfWorship.length,
        separatorBuilder: (_, __) => const SizedBox(height: 6),
        itemBuilder: (BuildContext context, int index) {
          return HomeItem(
            title: virtuesOfWorship[index]['title'],
            image: AssetsData.logo,
            hasBackground: true,
            onTap: () {
              GoRouter.of(context).push(
                AppRouter.kCustomView,
                extra: {
                  'id': virtuesOfWorship[index]['corder'],
                  'appBarTitle': virtuesOfWorship[index]['title'],
                },
              );
            },
          );
        },
      ),
    );
  }
}
